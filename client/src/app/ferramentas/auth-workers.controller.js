(function () {
    'use strict';

    angular
        .module('app')
        .controller('AuthWorkersController', AuthWorkersController);

    /** @ngInject */
    function AuthWorkersController($http, PROVISION_API, API_CONFIG, provisionSocket, $filter, $rootScope) {

        var vm = this;
        vm.userRoles = $rootScope.operador.role;
        vm.provisions = {
            'current': [],
            'queued': [],
            'failed': [],
            'canceled': [],
            'finished': []
        };
        vm.unauthorized = [];
        vm.today = 0;
        vm.limit = 20;
        vm.getProvisions = getProvisions;
        vm.activeTab = 'queued';
        vm.changeTab = changeTab;

        // Variáveis para logs do AuthService
        vm.authLogs = [];
        vm.loadingLogs = false;
        vm.logsSearched = false;
        vm.logFilters = {
            serial: '',
            olt_ip: '',
            data_inicio: '',
            data_fim: ''
        };

        vm.encodeUrl = function(url){
            return encodeURI(url);
        }

        activate();

        function activate() {
            getProvisions('queued');
            //getProvisions('failed');
           // getProvisions('canceled');
           // getProvisions('finished');


        };

        provisionSocket.on('provision_update', function (data) {

            // verifica se o id existe na lista atual
            var index = vm.provisions.current.findIndex(function (x) {
                return x.id === data.id;
            });

            //se existir e o status for diferente, remove da lista atual
            if (index > -1) {
                if(vm.activeTab != data.status){
                    vm.provisions.current.splice(index, 1);
                    vm.provisions.current = $filter('orderBy')(vm.provisions.current, 'enqueued_at', true);
                // caso seja o mesmo status, atualiza
                } else {
                    vm.provisions.current[index] = data;
                    vm.provisions.current = $filter('orderBy')(vm.provisions.current, 'enqueued_at', true);
                }
            // se não existir na lista atual e for o mesmo status, adiciona na lista
            } else {
                if(vm.activeTab == data.status){
                    vm.provisions.current.push(data);
                    vm.provisions.current = $filter('orderBy')(vm.provisions.current, 'enqueued_at', true);
                }
            }



        });

        function changeTab(tab){
            vm.activeTab = tab;
            vm.getProvisions(tab);

        }

        function getProvisions(status) {
            $http({
                url: PROVISION_API.url + "/provision/list/status/"+status,
                headers: {
                    'x-access-token': PROVISION_API.token,
                },
                method: "GET",
                ignoreLoadingBar: true
            }).then(function (response) {
                vm.provisions[status] = response.data;
                vm.provisions.current = vm.provisions[vm.activeTab]
            });
        };

        vm.requeueJob = function(id){
            $http({
                url: 'http://172.16.10.39:5005/rq/job/'+ id+"/requeue",
                method: "POST",
                ignoreLoadingBar: true
            }).then(function (response) {
                console.log(response.data)
            });
        }

        vm.getJobs = function(item){
            $http({
                url: PROVISION_API.url + "/provision/"+item.id+"/jobs",
                headers: {
                    'x-access-token': PROVISION_API.token,
                },
                method: "GET",
                ignoreLoadingBar: true
            }).then(function (response) {
                item.jobs = response.data;
            });
        }

        vm.itemclick = function (item) {
            if (item.hasOwnProperty('expanded')) {
                if (item['expanded'] == 1) {
                    item['expanded'] = 0;
                } else {
                    item['expanded'] = 1;
                }
            } else {

                item['expanded'] = 1;
            }

            if (item['expanded'] == 1) {
                this.getJobs(item);
            }

        }

        // Métodos para logs do AuthService
        vm.searchLogs = function() {
          console.log('aaaaaaaaa');
            vm.loadingLogs = true;
            vm.authLogs = [];

            var params = {};

            // Adiciona filtros se preenchidos
            if (vm.logFilters.serial && vm.logFilters.serial.trim()) {
                params.serial = vm.logFilters.serial.trim();
            }

            if (vm.logFilters.olt_ip && vm.logFilters.olt_ip.trim()) {
                params.olt_ip = vm.logFilters.olt_ip.trim();
            }

            if (vm.logFilters.data_inicio) {
                params.data_inicio = vm.logFilters.data_inicio;
            }

            if (vm.logFilters.data_fim) {
                params.data_fim = vm.logFilters.data_fim;
            }

            console.log('bbbbbbbbbbbbb');
            $http({
                url: API_CONFIG.url + "/ftth/auth-logs",
                method: "POST",
                data: params,
                headers: {
                    'Content-Type': 'application/json'
                },
                ignoreLoadingBar: true
            }).then(function (response) {
              console.log('ccccccccccccc');
                vm.authLogs = response.data;
                vm.loadingLogs = false;
                vm.logsSearched = true;
            }).catch(function (error) {
                console.error('Erro ao buscar logs:', error);
                vm.loadingLogs = false;
                vm.logsSearched = true;
                // Aqui você pode adicionar uma notificação de erro se tiver um sistema de toast
            });
        };

        vm.clearLogFilters = function() {
            vm.logFilters = {
                serial: '',
                olt_ip: '',
                data_inicio: '',
                data_fim: ''
            };
            vm.authLogs = [];
            vm.logsSearched = false;
        };

        vm.toggleExtraInfo = function(log) {
            log.showExtraInfo = !log.showExtraInfo;
        };

        vm.toggleErrorStack = function(log) {
            log.showErrorStack = !log.showErrorStack;
        };

    }

})();


