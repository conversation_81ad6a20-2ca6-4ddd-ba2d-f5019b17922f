(function () {
    'use strict';

    angular
        .module('app')
        .controller('AuthWorkersController', AuthWorkersController);

    /** @ngInject */
    function AuthWorkersController($http, PROVISION_API, API_CONFIG, provisionSocket, $filter, $rootScope, $timeout) {

        var vm = this;
        vm.userRoles = $rootScope.operador.role;
        vm.provisions = {
            'current': [],
            'queued': [],
            'failed': [],
            'canceled': [],
            'finished': []
        };
        vm.unauthorized = [];
        vm.today = 0;
        vm.limit = 20;
        vm.getProvisions = getProvisions;
        vm.activeTab = 'queued';
        vm.changeTab = changeTab;

        // Variáveis para logs do AuthService
        vm.authLogs = [];
        vm.loadingLogs = false;
        vm.logsSearched = false;
        vm.logFilters = {
            serial: '',
            olt_ip: '',
            data_inicio: null,
            data_fim: null
        };

        // Função para formatar data para PostgreSQL
        vm.formatDateForPostgres = function(dateInput) {
            console.log('formatDateForPostgres recebeu:', dateInput, 'tipo:', typeof dateInput);

            if (!dateInput) return null;

            var date;
            // Se é uma string, converte para Date
            if (typeof dateInput === 'string') {
                date = new Date(dateInput);
                console.log('Converteu string para Date:', date);
            } else if (dateInput instanceof Date) {
                // Se já é um objeto Date, usa diretamente
                date = dateInput;
                console.log('Já é um objeto Date:', date);
            } else {
                console.log('Tipo não reconhecido, retornando null');
                return null;
            }

            if (isNaN(date.getTime())) {
                console.log('Data inválida, retornando null');
                return null;
            }

            // Formato PostgreSQL: YYYY-MM-DD HH:MM:SS
            var year = date.getFullYear();
            var month = vm.pad(date.getMonth() + 1);
            var day = vm.pad(date.getDate());
            var hours = vm.pad(date.getHours());
            var minutes = vm.pad(date.getMinutes());
            var seconds = vm.pad(date.getSeconds());

            var resultado = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
            console.log('Resultado formatado:', resultado);
            return resultado;
        };

        // Função auxiliar para adicionar zero à esquerda
        vm.pad = function(num) {
            return (num < 10 ? '0' : '') + num;
        };

        // Função para preencher datas padrão
        vm.setDefaultDates = function() {
            var now = new Date();
            var today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // Para AngularJS, vamos usar objetos Date diretamente
            vm.logFilters.data_inicio = today; // 00:00 do dia atual
            vm.logFilters.data_fim = now; // momento atual
        };

        vm.encodeUrl = function(url){
            return encodeURI(url);
        }

        activate();

        function activate() {
            getProvisions('queued');
            //getProvisions('failed');
           // getProvisions('canceled');
           // getProvisions('finished');

           // Define as datas padrão para os filtros de log
           vm.setDefaultDates();

        };

        provisionSocket.on('provision_update', function (data) {

            // verifica se o id existe na lista atual
            var index = vm.provisions.current.findIndex(function (x) {
                return x.id === data.id;
            });

            //se existir e o status for diferente, remove da lista atual
            if (index > -1) {
                if(vm.activeTab != data.status){
                    vm.provisions.current.splice(index, 1);
                    vm.provisions.current = $filter('orderBy')(vm.provisions.current, 'enqueued_at', true);
                // caso seja o mesmo status, atualiza
                } else {
                    vm.provisions.current[index] = data;
                    vm.provisions.current = $filter('orderBy')(vm.provisions.current, 'enqueued_at', true);
                }
            // se não existir na lista atual e for o mesmo status, adiciona na lista
            } else {
                if(vm.activeTab == data.status){
                    vm.provisions.current.push(data);
                    vm.provisions.current = $filter('orderBy')(vm.provisions.current, 'enqueued_at', true);
                }
            }



        });

        function changeTab(tab){
            vm.activeTab = tab;

            // Só busca provisions se não for a aba de logs
            if(tab !== 'log_detalhado') {
                vm.getProvisions(tab);
            }

        }

        function getProvisions(status) {
            $http({
                url: PROVISION_API.url + "/provision/list/status/"+status,
                headers: {
                    'x-access-token': PROVISION_API.token,
                },
                method: "GET",
                ignoreLoadingBar: true
            }).then(function (response) {
                vm.provisions[status] = response.data;
                vm.provisions.current = vm.provisions[vm.activeTab]
            });
        };

        vm.requeueJob = function(id){
            $http({
                url: 'http://172.16.10.39:5005/rq/job/'+ id+"/requeue",
                method: "POST",
                ignoreLoadingBar: true
            }).then(function (response) {
                console.log(response.data)
            });
        }

        vm.getJobs = function(item){
            $http({
                url: PROVISION_API.url + "/provision/"+item.id+"/jobs",
                headers: {
                    'x-access-token': PROVISION_API.token,
                },
                method: "GET",
                ignoreLoadingBar: true
            }).then(function (response) {
                item.jobs = response.data;
            });
        }

        vm.itemclick = function (item) {
            if (item.hasOwnProperty('expanded')) {
                if (item['expanded'] == 1) {
                    item['expanded'] = 0;
                } else {
                    item['expanded'] = 1;
                }
            } else {

                item['expanded'] = 1;
            }

            if (item['expanded'] == 1) {
                this.getJobs(item);
            }

        }

        // Métodos para logs do AuthService
        vm.searchLogs = function() {
            console.log('Iniciando busca de logs...');
            vm.loadingLogs = true;
            vm.authLogs = [];

            var params = {};

            // Adiciona filtros se preenchidos
            if (vm.logFilters.serial && vm.logFilters.serial.trim()) {
                params.serial = vm.logFilters.serial.trim();
            }

            if (vm.logFilters.olt_ip && vm.logFilters.olt_ip.trim()) {
                params.olt_ip = vm.logFilters.olt_ip.trim();
            }

            // Formata as datas para PostgreSQL
            console.log('Filtros antes da formatação:', vm.logFilters);

            if (vm.logFilters.data_inicio) {
                var dataInicioFormatada = vm.formatDateForPostgres(vm.logFilters.data_inicio);
                console.log('Data início formatada:', dataInicioFormatada);
                params.data_inicio = dataInicioFormatada;
            }

            if (vm.logFilters.data_fim) {
                var dataFimFormatada = vm.formatDateForPostgres(vm.logFilters.data_fim);
                console.log('Data fim formatada:', dataFimFormatada);
                params.data_fim = dataFimFormatada;
            }

            console.log('Parâmetros enviados:', params);

            $http({
                url: API_CONFIG.url + "/ftth/auth-logs",
                method: "POST",
                data: params,
                headers: {
                    'Content-Type': 'application/json'
                },
                ignoreLoadingBar: true
            }).then(function (response) {
                console.log('Resposta recebida:', response.data);
                vm.authLogs = response.data;
                vm.loadingLogs = false;
                vm.logsSearched = true;
            }).catch(function (error) {
                console.error('Erro ao buscar logs:', error);
                vm.loadingLogs = false;
                vm.logsSearched = true;
                // Aqui você pode adicionar uma notificação de erro se tiver um sistema de toast
            });
        };

        vm.clearLogFilters = function() {
            vm.logFilters.serial = '';
            vm.logFilters.olt_ip = '';
            // Preenche as datas padrão (não limpa, só redefine)
            vm.setDefaultDates();
            vm.authLogs = [];
            vm.logsSearched = false;
        };

        vm.toggleExtraInfo = function(log) {
            log.showExtraInfo = !log.showExtraInfo;
        };

        vm.toggleErrorStack = function(log) {
            log.showErrorStack = !log.showErrorStack;
        };

        vm.searchLogsBySerial = function(serial) {
            // Limpa apenas os filtros de texto, mantém as datas
            vm.logFilters.serial = serial;
            vm.logFilters.olt_ip = '';

            // Se as datas não estão definidas, define as padrão
            if (!vm.logFilters.data_inicio || !vm.logFilters.data_fim) {
                vm.setDefaultDates();
            }

            // Muda para a aba de logs detalhados
            vm.changeTab('log_detalhado');

            // Ativa a aba visualmente
            $('.nav-tabs a[data-target="#log_detalhado"]').tab('show');

            // Executa a busca após um pequeno delay para garantir que a aba foi carregada
            $timeout(function() {
                vm.searchLogs();
            }, 100);
        };

    }

})();


