<ol class="breadcrumb">
    <li><a href="/"><i class="glyphicon glyphicon-home"></i> Dashboard</a></li>
    <li><i class="glyphicon glyphicon-wrench"></i> Ferramentas</a></li>
    <li><i class="glyphicon glyphicon-cloud-upload"></i> Serviço de Provisionamento</li>
</ol>

<div class="container-fluid">




<div class="row">
    <a href="http://172.16.10.39:5005/rq" target="_blank" authorize="develop.write" class="btn btn-default btn-sm"><i class="glyphicon glyphicon-dashboard"></i> RQ Dashboard</a>
    <a href="*************************************************" target="_blank" authorize="develop.write" class="btn btn-default btn-sm"><i class="glyphicon glyphicon-list-alt"></i> Supervisor</a>
    <br><br>
                                <div><svg class="blinking" width="8" height="6">
                                    <circle fill="#337ab7" r="3" cx="3" cy="3"></circle>
                                </svg> <i>*Atualizada em tempo real</i></div>

</div>
    <div class="row">
        <ul class="nav nav-tabs">
            <li class="active">
              <a data-target="#lista" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('queued')">
                <i class="glyphicon glyphicon-indent-left"></i> <b>Na fila </b></a>
            </li>
            <li>
              <a data-target="#lista" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('failed')">
                <i class="glyphicon glyphicon-remove-sign"></i> <b>Falha </b></a>
            </li>
            <li>
              <a data-target="#lista" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('canceled')">
                <i class="glyphicon glyphicon-ban-circle"></i> <b>Cancelados </b></a>
            </li>
            <li>
              <a data-target="#lista" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('finished')">
                <i class="glyphicon glyphicon-ok-sign"></i> <b>Finalizados </b></a>
            </li>
            <li ng-if="HDC.userRoles.includes('root')">
              <a data-target="#log_detalhado" data-toggle="tab" style="cursor: pointer;" ng-click="AWC.changeTab('log_detalhado')">
                <i class="glyphicon glyphicon-list"></i> <b>LOG DETALHADO </b></a>
            </li>
          </ul>

          <div class="tab-content">

            <div class="tab-pane active" id="lista">
        <div class="table-responsive">
<table class="table table-hover table-striped table-bordered align-center valign-middle">

    <thead>
        <tr>
            <th></th>
            <th>Status</th>
            <th>Data</th>
            <th>Username</th>
            <th>Serial</th>
            <th>Modelo</th>
            <th>OLT</th>
            <th>OLT IP</th>
            <th>OLT Modelo</th>
            <th>Placa</th>
            <th>Porta</th>
            <th>Info</th>
        </tr>
    </thead>

    <tbody>

        <tr class="clickable" ng-repeat-start="item in AWC.provisions.current">
            <td style="cursor: pointer;" title="Mais informações" ng-click="AWC.itemclick(item)"
                data-toggle="collapse" data-target="#details-{{item.id}}">
                <span class="glyphicon" ng-class="[{'glyphicon-chevron-down': item.expanded != 1}, {'glyphicon-chevron-up': item.expanded == 1} ]" style="cursor: pointer;"></span>
            </td>
            <td><span class="label"
                ng-class="[{'label-success': item.status == 'finished'},
                          {'label-warning': item.status == 'queued'},
                          {'label-default': item.status == 'started'},
                          {'label-danger': item.status == 'failed' || item.status == 'canceled'}]">{{item.status}}</span>
            </td>

            <td>{{item.enqueued_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
            <td><a href="/helpdesk?username={{item.username}}" target="_blank">{{item.username}}</a></td>
            <td>{{item.serial}}</td>
            <td>{{item.model}}</td>
            <td>{{item.olt}}</td>
            <td>{{item.olt_ip}}</td>
            <td>{{item.olt_model}}</td>
            <td>{{item.slot}}</td>
            <td>{{item.pon}}</td>
            <td>{{item.exc_info}}</td>
        </tr>
        <tr ng-repeat-end>
            <td colspan="13" style="padding: 0 !important;">
                <div id="details-{{item.id}}" class="collapse">

                    <div class="col-md-5 vert-align">

                        <table class="table table-bordered table-hover">
                            <caption>Jobs <button class="btn btn-default btn-sm"
                                ng-click="AWC.getJobs(item)"><i class="glyphicon glyphicon-refresh"></i> Atualiza</button></caption>

                            <thead>
                                <tr>
                                    <th>Status</th>
                                    <th>Descrição</th>
                                    <th>Data Início</th>
                                    <th>Data Fim</th>
                                    <th>Info</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="job in item.jobs">
                                    <td><span class="label"
                                        ng-class="[{'label-success': job.status == 'finished'},
                                                  {'label-warning': job.status == 'queued'},
                                                  {'label-default': job.status == 'deferred'},
                                                  {'label-default': job.status == 'started'},
                                                  {'label-danger': job.status == 'failed' || job.status == 'canceled'}]">{{job.status}}</td>
                                    <td>{{job.description}}</td>
                                    <td>{{job.started_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                                    <td>{{job.ended_at | amDateFormat:'DD/MM/YYYY HH:mm:ss'}}</td>
                                    <td>{{job.exc_info}}</td>
                                    <td><button ng-if="job.status == 'failed'" class="btn btn-sm btn-default" ng-click="AWC.requeueJob(job.id)"><i class="glyphicon glyphicon-repeat"></i> Reiniciar</button></td>

                                </tr>
                            </tbody>


                        </table>

                    </div>
            </td>
        </tr>
    </tbody>
</table>
<p>
    <b>Na Fila</b> Exibe os provisionamentos que atendem os requisitos e entraram na fila <br>
    <b>Falha</b> Exibe os provisionamentos que tiveram falha em algum processo do provisionamento <br>
    <b>Cancelados</b> Exibe os provisionamentos que não entraram na fila por não atenderem um ou mais requisitos <br>
    <b>Finalizados</b> Exibe os provisionamentos concluídos com sucesso
</p>
</div>



</div>

<div id="log_detalhado" class="tab-pane fade in">
  Teste
</div>
<!-- Tab content -->
</div>


</div>
</div>

