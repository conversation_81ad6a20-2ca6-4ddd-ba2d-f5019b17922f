# API de Logs do AuthService - Exemplo de Implementação

## Endpoint
```
GET /auth/logs
```

## Parâmetros de Query (opcionais)
- `serial`: Serial da ONU (string)
- `olt_ip`: IP da OLT (string)
- `data_inicio`: Data de início no formato ISO 8601 (string)
- `data_fim`: Data de fim no formato ISO 8601 (string)

## Exemplo de Requisição
```
GET /auth/logs?serial=FHTT12345678&olt_ip=***********&data_inicio=2024-01-01T00:00:00&data_fim=2024-01-31T23:59:59
```

## Exemplo de Resposta
```json
[
  {
    "id": 1,
    "created_at": "2024-01-15T10:30:00.000Z",
    "flag": "INFO",
    "message": "ONU provisionada com sucesso",
    "extra_info": {
      "config_applied": {
        "vlan": 100,
        "bandwidth": "100M",
        "profile": "residential"
      },
      "response_time": "2.3s"
    },
    "error": false,
    "error_message": null,
    "error_stack": null,
    "onu_model": "HG8245H",
    "onu_serial": "FHTT12345678",
    "onu_mac": "00:11:22:33:44:55",
    "onu_username": "user123",
    "acs_identifier": "acs_001",
    "olt_ip": "***********",
    "olt_name": "OLT-CENTRO-01",
    "slot": "1",
    "pon": "8",
    "process": "provision_onu"
  },
  {
    "id": 2,
    "created_at": "2024-01-15T11:45:00.000Z",
    "flag": "ERROR",
    "message": "Falha ao configurar ONU",
    "extra_info": {
      "attempted_config": {
        "vlan": 200,
        "bandwidth": "50M"
      },
      "retry_count": 3
    },
    "error": true,
    "error_message": "Timeout na comunicação com a ONU",
    "error_stack": "Error: Timeout\n    at ONUManager.configure (onu-manager.js:45:12)\n    at async provision (provision.js:123:5)",
    "onu_model": "HG8240H",
    "onu_serial": "FHTT87654321",
    "onu_mac": "00:AA:BB:CC:DD:EE",
    "onu_username": "user456",
    "acs_identifier": "acs_002",
    "olt_ip": "***********",
    "olt_name": "OLT-BAIRRO-02",
    "slot": "2",
    "pon": "4",
    "process": "configure_onu"
  }
]
```

## Implementação Sugerida no Backend

### SQL Query Base
```sql
SELECT * FROM authservice.logs 
WHERE 1=1
  AND ($1::varchar IS NULL OR onu_serial ILIKE '%' || $1 || '%')
  AND ($2::varchar IS NULL OR olt_ip = $2)
  AND ($3::timestamp IS NULL OR created_at >= $3)
  AND ($4::timestamp IS NULL OR created_at <= $4)
ORDER BY created_at DESC
LIMIT 1000;
```

### Exemplo em Node.js/Express
```javascript
app.get('/auth/logs', async (req, res) => {
  try {
    const { serial, olt_ip, data_inicio, data_fim } = req.query;
    
    const params = [
      serial || null,
      olt_ip || null,
      data_inicio || null,
      data_fim || null
    ];
    
    const result = await db.query(sqlQuery, params);
    res.json(result.rows);
  } catch (error) {
    console.error('Erro ao buscar logs:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});
```

## Notas Importantes

1. **Paginação**: Considere implementar paginação para grandes volumes de dados
2. **Índices**: Crie índices nas colunas mais consultadas (onu_serial, olt_ip, created_at)
3. **Limite**: Implemente um limite máximo de registros retornados
4. **Validação**: Valide os parâmetros de entrada para evitar SQL injection
5. **Cache**: Considere cache para consultas frequentes
